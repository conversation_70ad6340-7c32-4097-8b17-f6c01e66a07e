<template>
  <div class="app">
    <!-- Header -->
    <header class="app-header">
      <div class="container">
        <div class="flex items-center justify-between py-lg">
          <div class="app-title">
            <h1 class="text-3xl font-bold text-primary">Simonitor</h1>
            <p class="text-muted">Sims 4 Package Analyzer & Mod Manager</p>
          </div>
          <div class="app-actions flex items-center gap-sm">
            <button class="btn btn-secondary btn-sm" @click="openSettings">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
              </svg>
              Settings
            </button>
            <span class="badge badge-info">v1.0.0</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="app-main">
      <div class="container">
        <!-- File Upload Section -->
        <section class="upload-section mb-xl">
          <div class="card">
            <FileUpload
              @files-selected="handleFilesSelected"
              @analyze-requested="handleAnalyzeRequested"
              ref="fileUploadRef"
            />
          </div>
        </section>

        <!-- Loading State -->
        <section v-if="isAnalyzing" class="loading-section mb-xl">
          <div class="card text-center py-xl">
            <div class="loading-content">
              <div class="loading mb-md" style="width: 2rem; height: 2rem; margin: 0 auto;"></div>
              <h3 class="font-medium mb-sm">Analyzing Files...</h3>
              <p class="text-muted">
                Processing {{ selectedFiles.length }} file(s). This may take a moment.
              </p>
              <div class="progress-info text-sm text-muted mt-md">
                {{ analyzedCount }} of {{ selectedFiles.length }} files completed
              </div>
            </div>
          </div>
        </section>

        <!-- Error Display -->
        <section v-if="analysisError" class="error-section mb-xl">
          <div class="card">
            <div class="error-content">
              <div class="flex items-center gap-md mb-md">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-error">
                  <circle cx="12" cy="12" r="10"/>
                  <line x1="15" y1="9" x2="9" y2="15"/>
                  <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                <h3 class="font-medium text-error">Analysis Error</h3>
              </div>
              <p class="text-muted mb-md">{{ analysisError }}</p>
              <button class="btn btn-secondary btn-sm" @click="clearError">
                Dismiss
              </button>
            </div>
          </div>
        </section>

        <!-- Results Section -->
        <section v-if="analysisResults.length > 0" class="results-section">
          <div class="card">
            <AnalysisResults :results="analysisResults" />
          </div>
        </section>

        <!-- Welcome State -->
        <section v-if="!isAnalyzing && analysisResults.length === 0 && !analysisError" class="welcome-section">
          <div class="welcome-content text-center py-xl">
            <div class="welcome-icon mb-lg">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" class="text-muted">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
            </div>
            <h2 class="text-2xl font-semibold mb-md">Welcome to Simonitor</h2>
            <p class="text-muted mb-lg max-w-md mx-auto">
              Upload your Sims 4 .package or .ts4script files to analyze their contents, 
              detect conflicts, and manage your mods effectively.
            </p>
            <div class="welcome-features">
              <div class="features-grid">
                <div class="feature-item">
                  <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M9 12l2 2 4-4"/>
                      <circle cx="12" cy="12" r="10"/>
                    </svg>
                  </div>
                  <h4 class="font-medium">Resource Analysis</h4>
                  <p class="text-sm text-muted">Detailed analysis of package contents</p>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                      <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                    </svg>
                  </div>
                  <h4 class="font-medium">Export Results</h4>
                  <p class="text-sm text-muted">Export analysis data as JSON or CSV</p>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <circle cx="11" cy="11" r="8"/>
                      <path d="m21 21-4.35-4.35"/>
                    </svg>
                  </div>
                  <h4 class="font-medium">Search & Filter</h4>
                  <p class="text-sm text-muted">Find specific resources quickly</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <div class="container">
        <div class="text-center py-md text-sm text-muted">
          <p>Simonitor - Built with ❤️ for the Sims 4 community</p>
        </div>
      </div>
    </footer>

    <!-- Settings Modal -->
    <Modal
      :is-open="isSettingsOpen"
      title="Settings"
      @close="closeSettings"
    >
      <AppSettings
        @settings-changed="handleSettingsChanged"
        ref="settingsRef"
      />
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import FileUpload from './components/FileUpload.vue';
import AnalysisResults from './components/AnalysisResults.vue';
import Modal from './components/Modal.vue';
import AppSettings from './components/AppSettings.vue';

// Types
import type { AnalyzedPackage, ResourceInfo } from '../types/analysis';

// Reactive state
const selectedFiles = ref<File[]>([]);
const analysisResults = ref<AnalyzedPackage[]>([]);
const isAnalyzing = ref(false);
const analyzedCount = ref(0);
const analysisError = ref<string | null>(null);
const fileUploadRef = ref();
const isSettingsOpen = ref(false);
const settingsRef = ref();

// Event handlers
function handleFilesSelected(files: File[]) {
  selectedFiles.value = files;
  // Clear previous results when new files are selected
  if (files.length === 0) {
    analysisResults.value = [];
    analysisError.value = null;
  }
}

async function handleAnalyzeRequested(files: File[]) {
  if (files.length === 0) return;
  
  isAnalyzing.value = true;
  analyzedCount.value = 0;
  analysisResults.value = [];
  analysisError.value = null;
  
  try {
    // Analyze files one by one
    for (const file of files) {
      const filePath = (file as any).path;
      
      // Create a promise to handle the async analysis
      const analysisPromise = new Promise<AnalyzedPackage>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Analysis timeout'));
        }, 30000); // 30 second timeout
        
        const handleResult = (result: any) => {
          clearTimeout(timeout);
          window.electronAPI.offAnalysisResult?.(handleResult);
          
          if (result.error) {
            reject(new Error(result.error));
          } else {
            resolve(result);
          }
        };
        
        window.electronAPI.onAnalysisResult(handleResult);
        window.electronAPI.analyzePackage(filePath);
      });
      
      try {
        const result = await analysisPromise;
        analysisResults.value.push(result);
        analyzedCount.value++;
      } catch (error) {
        console.error(`Error analyzing ${file.name}:`, error);
        analysisError.value = `Error analyzing ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        break;
      }
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'An unexpected error occurred';
  } finally {
    isAnalyzing.value = false;
  }
}

function clearError() {
  analysisError.value = null;
}

// Settings handlers
function openSettings() {
  isSettingsOpen.value = true;
}

function closeSettings() {
  isSettingsOpen.value = false;
}

function handleSettingsChanged(settings: any) {
  // Handle settings changes - could be used to update app behavior
  console.log('Settings updated:', settings);
}

// Set up the analysis result handler
window.electronAPI.onAnalysisResult((result: any) => {
  // This will be handled by the promise in handleAnalyzeRequested
});
</script>

<style>
/* Import our design system */
@import './styles/design-system.css';

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}

.app-header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.app-main {
  flex: 1;
  padding: var(--spacing-xl) 0;
}

.app-footer {
  background-color: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.welcome-content {
  max-width: 600px;
  margin: 0 auto;
}

.welcome-icon {
  color: var(--text-muted);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-item {
  text-align: center;
  padding: var(--spacing-lg);
}

.feature-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.feature-item h4 {
  margin: 0 0 var(--spacing-sm) 0;
}

.feature-item p {
  margin: 0;
}

.max-w-md {
  max-width: 28rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.text-error {
  color: var(--error-color);
}

.loading-content .loading {
  border-width: 3px;
}

@media (max-width: 768px) {
  .app-title h1 {
    font-size: 1.5rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .feature-item {
    padding: var(--spacing-md);
  }
}
</style>