import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

/**
 * The `contextBridge` is a secure way to expose APIs from the main process
 * to the renderer process. We will define a clear, typed API here for
 * all our inter-process communication.
 */
contextBridge.exposeInMainWorld('electronAPI', {
    /**
     * Sends a file path to the main process for analysis.
     * @param filePath The absolute path of the file to analyze.
     */
    analyzePackage: (filePath: string) => ipcRenderer.send('analyze-package', filePath),

    /**
     * Subscribes to the 'analysis-result' channel to receive analysis results
     * from the main process.
     * @param callback The function to execute when a result is received.
     */
    onAnalysisResult: (callback: (result: any) => void) => {
        ipcRenderer.on('analysis-result', (_event, result) => {
            callback(result);
        });
    },
});